import { _decorator, Component, tween, Vec3 } from 'cc';
import { GameBoard, TargetData } from './GameBoard';
const { ccclass, property } = _decorator;

/**
 * 消除结果数据
 */
export interface ClearResult {
    clearedRows: number[];
    clearedCols: number[];
    totalCleared: number;
    score: number;
}

/**
 * 消除动画配置
 */
export interface ClearAnimationConfig {
    duration: number;
    delay: number;
    scaleEffect: boolean;
    fadeEffect: boolean;
}

/**
 * 消除判断系统
 */
@ccclass('ClearSystem')
export class ClearSystem extends Component {
    
    @property
    public scorePerCell: number = 10;  // 每个格子的分数

    @property
    public comboMultiplier: number = 1.5;  // 连击倍数

    @property
    public animationDuration: number = 0.5;  // 动画持续时间

    private _gameBoard: GameBoard = null;
    private _currentCombo: number = 0;
    private _totalScore: number = 0;

    // 事件回调
    private _onClearStart: (result: ClearResult) => void = null;
    private _onClearComplete: (result: ClearResult) => void = null;
    private _onScoreUpdate: (score: number, combo: number) => void = null;

    protected onLoad() {
        // 获取游戏棋盘组件
        this._gameBoard = this.getComponent(GameBoard);
        if (!this._gameBoard) {
            this._gameBoard = this.node.parent?.getComponent(GameBoard);
        }
    }

    /**
     * 检查并执行消除
     */
    public checkAndClear(): ClearResult {
        if (!this._gameBoard) {
            return this.createEmptyResult();
        }

        // 获取需要消除的行列
        const clearData = this._gameBoard.checkAndClearLines();
        
        // 创建消除结果
        const result: ClearResult = {
            clearedRows: clearData.clearedRows,
            clearedCols: clearData.clearedCols,
            totalCleared: clearData.clearedRows.length + clearData.clearedCols.length,
            score: this.calculateScore(clearData.clearedRows.length, clearData.clearedCols.length)
        };

        // 更新连击和分数
        if (result.totalCleared > 0) {
            this._currentCombo++;
            this._totalScore += result.score;
            
            // 触发消除开始事件
            if (this._onClearStart) {
                this._onClearStart(result);
            }

            // 播放消除动画
            this.playClearAnimation(result);
        } else {
            // 没有消除，重置连击
            this._currentCombo = 0;
        }

        // 触发分数更新事件
        if (this._onScoreUpdate) {
            this._onScoreUpdate(this._totalScore, this._currentCombo);
        }

        return result;
    }

    /**
     * 计算分数
     */
    private calculateScore(rowCount: number, colCount: number): number {
        const baseScore = (rowCount + colCount) * this.scorePerCell;
        const comboBonus = Math.pow(this.comboMultiplier, Math.max(0, this._currentCombo - 1));
        return Math.floor(baseScore * comboBonus);
    }

    /**
     * 播放消除动画
     */
    private playClearAnimation(result: ClearResult): void {
        // 这里可以添加具体的动画效果
        // 例如：闪烁效果、缩放效果等
        
        // 延迟触发消除完成事件
        this.scheduleOnce(() => {
            if (this._onClearComplete) {
                this._onClearComplete(result);
            }
        }, this.animationDuration);
    }

    /**
     * 创建空的消除结果
     */
    private createEmptyResult(): ClearResult {
        return {
            clearedRows: [],
            clearedCols: [],
            totalCleared: 0,
            score: 0
        };
    }

    /**
     * 检查特定行是否满足消除条件
     */
    public checkRowClearCondition(row: number): boolean {
        if (!this._gameBoard) {
            return false;
        }

        const targets = this._gameBoard.getTargets();
        const filledCount = this._gameBoard.getRowFilledCount(row);
        return filledCount === targets.rowTargets[row];
    }

    /**
     * 检查特定列是否满足消除条件
     */
    public checkColClearCondition(col: number): boolean {
        if (!this._gameBoard) {
            return false;
        }

        const targets = this._gameBoard.getTargets();
        const filledCount = this._gameBoard.getColFilledCount(col);
        return filledCount === targets.colTargets[col];
    }

    /**
     * 获取可消除的行列预览
     */
    public getClearableLines(): { rows: number[], cols: number[] } {
        const rows: number[] = [];
        const cols: number[] = [];

        if (!this._gameBoard) {
            return { rows, cols };
        }

        const boardSize = this._gameBoard.getBoardSize();

        // 检查所有行
        for (let row = 0; row < boardSize; row++) {
            if (this.checkRowClearCondition(row)) {
                rows.push(row);
            }
        }

        // 检查所有列
        for (let col = 0; col < boardSize; col++) {
            if (this.checkColClearCondition(col)) {
                cols.push(col);
            }
        }

        return { rows, cols };
    }

    /**
     * 重置分数和连击
     */
    public resetScore(): void {
        this._totalScore = 0;
        this._currentCombo = 0;
        
        if (this._onScoreUpdate) {
            this._onScoreUpdate(this._totalScore, this._currentCombo);
        }
    }

    /**
     * 获取当前分数
     */
    public getTotalScore(): number {
        return this._totalScore;
    }

    /**
     * 获取当前连击数
     */
    public getCurrentCombo(): number {
        return this._currentCombo;
    }

    /**
     * 设置事件回调
     */
    public setClearStartCallback(callback: (result: ClearResult) => void): void {
        this._onClearStart = callback;
    }

    public setClearCompleteCallback(callback: (result: ClearResult) => void): void {
        this._onClearComplete = callback;
    }

    public setScoreUpdateCallback(callback: (score: number, combo: number) => void): void {
        this._onScoreUpdate = callback;
    }

    /**
     * 设置分数配置
     */
    public setScorePerCell(score: number): void {
        this.scorePerCell = Math.max(1, score);
    }

    public setComboMultiplier(multiplier: number): void {
        this.comboMultiplier = Math.max(1, multiplier);
    }

    /**
     * 设置动画持续时间
     */
    public setAnimationDuration(duration: number): void {
        this.animationDuration = Math.max(0.1, duration);
    }
}
