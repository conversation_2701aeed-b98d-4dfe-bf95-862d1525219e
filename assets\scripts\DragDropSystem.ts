import { _decorator, Component, Node, Vec2, Vec3, EventTouch, Camera, geometry } from 'cc';
import { TetrisBlockData } from './TetrisBlock';
import { GameBoard } from './GameBoard';
const { ccclass, property } = _decorator;

/**
 * 拖拽状态枚举
 */
export enum DragState {
    IDLE = 'idle',
    DRAGGING = 'dragging',
    PLACING = 'placing'
}

/**
 * 拖拽事件数据
 */
export interface DragEventData {
    blockData: TetrisBlockData;
    startPosition: Vec3;
    currentPosition: Vec3;
    boardPosition: Vec2;  // 在棋盘上的位置
    isValidPosition: boolean;
}

/**
 * 拖拽放置系统
 */
@ccclass('DragDropSystem')
export class DragDropSystem extends Component {
    
    @property(Camera)
    public camera: Camera = null;

    @property(Node)
    public gameBoard: Node = null;

    @property
    public gridSize: number = 50;  // 网格大小

    @property
    public snapThreshold: number = 25;  // 吸附阈值

    private _currentDragState: DragState = DragState.IDLE;
    private _draggedBlock: TetrisBlockData = null;
    private _draggedNode: Node = null;
    private _startPosition: Vec3 = new Vec3();
    private _boardComponent: GameBoard = null;
    private _dragEventData: DragEventData = null;

    // 事件回调
    private _onDragStart: (data: DragEventData) => void = null;
    private _onDragMove: (data: DragEventData) => void = null;
    private _onDragEnd: (data: DragEventData, success: boolean) => void = null;

    protected onLoad() {
        if (this.gameBoard) {
            this._boardComponent = this.gameBoard.getComponent(GameBoard);
        }
    }

    /**
     * 开始拖拽方块
     */
    public startDrag(blockData: TetrisBlockData, dragNode: Node, startPos: Vec3): void {
        if (this._currentDragState !== DragState.IDLE) {
            return;
        }

        this._currentDragState = DragState.DRAGGING;
        this._draggedBlock = blockData;
        this._draggedNode = dragNode;
        this._startPosition.set(startPos);

        // 创建拖拽事件数据
        this._dragEventData = {
            blockData: blockData,
            startPosition: new Vec3(startPos),
            currentPosition: new Vec3(startPos),
            boardPosition: new Vec2(-1, -1),
            isValidPosition: false
        };

        // 触发拖拽开始事件
        if (this._onDragStart) {
            this._onDragStart(this._dragEventData);
        }
    }

    /**
     * 更新拖拽位置
     */
    public updateDrag(currentPos: Vec3): void {
        if (this._currentDragState !== DragState.DRAGGING || !this._draggedNode) {
            return;
        }

        // 更新节点位置
        this._draggedNode.setPosition(currentPos);
        this._dragEventData.currentPosition.set(currentPos);

        // 计算在棋盘上的位置
        const boardPos = this.worldToBoardPosition(currentPos);
        this._dragEventData.boardPosition.set(boardPos);

        // 检查位置是否有效
        this._dragEventData.isValidPosition = this.isValidPlacement(boardPos);

        // 触发拖拽移动事件
        if (this._onDragMove) {
            this._onDragMove(this._dragEventData);
        }
    }

    /**
     * 结束拖拽
     */
    public endDrag(): boolean {
        if (this._currentDragState !== DragState.DRAGGING) {
            return false;
        }

        this._currentDragState = DragState.PLACING;
        let success = false;

        // 尝试放置方块
        if (this._dragEventData.isValidPosition && this._boardComponent) {
            const boardPos = this._dragEventData.boardPosition;
            success = this._boardComponent.placeBlock(
                this._draggedBlock, 
                Math.floor(boardPos.x), 
                Math.floor(boardPos.y)
            );
        }

        // 如果放置失败，将方块返回原位置
        if (!success && this._draggedNode) {
            this._draggedNode.setPosition(this._startPosition);
        }

        // 触发拖拽结束事件
        if (this._onDragEnd) {
            this._onDragEnd(this._dragEventData, success);
        }

        // 重置状态
        this.resetDragState();
        return success;
    }

    /**
     * 取消拖拽
     */
    public cancelDrag(): void {
        if (this._currentDragState === DragState.IDLE) {
            return;
        }

        // 将方块返回原位置
        if (this._draggedNode) {
            this._draggedNode.setPosition(this._startPosition);
        }

        // 触发拖拽结束事件（失败）
        if (this._onDragEnd && this._dragEventData) {
            this._onDragEnd(this._dragEventData, false);
        }

        this.resetDragState();
    }

    /**
     * 重置拖拽状态
     */
    private resetDragState(): void {
        this._currentDragState = DragState.IDLE;
        this._draggedBlock = null;
        this._draggedNode = null;
        this._dragEventData = null;
    }

    /**
     * 将世界坐标转换为棋盘坐标
     */
    private worldToBoardPosition(worldPos: Vec3): Vec2 {
        if (!this.gameBoard) {
            return new Vec2(-1, -1);
        }

        // 将世界坐标转换为棋盘本地坐标
        const boardWorldPos = this.gameBoard.getWorldPosition();
        const localPos = new Vec3(
            worldPos.x - boardWorldPos.x,
            worldPos.y - boardWorldPos.y,
            0
        );

        // 转换为网格坐标
        const gridX = Math.floor(localPos.x / this.gridSize);
        const gridY = Math.floor(-localPos.y / this.gridSize);  // Y轴翻转

        return new Vec2(gridX, gridY);
    }

    /**
     * 检查位置是否有效
     */
    private isValidPlacement(boardPos: Vec2): boolean {
        if (!this._boardComponent || !this._draggedBlock) {
            return false;
        }

        return this._boardComponent.canPlaceBlock(
            this._draggedBlock,
            Math.floor(boardPos.x),
            Math.floor(boardPos.y)
        );
    }

    /**
     * 获取当前拖拽状态
     */
    public getDragState(): DragState {
        return this._currentDragState;
    }

    /**
     * 检查是否正在拖拽
     */
    public isDragging(): boolean {
        return this._currentDragState === DragState.DRAGGING;
    }

    /**
     * 设置事件回调
     */
    public setDragStartCallback(callback: (data: DragEventData) => void): void {
        this._onDragStart = callback;
    }

    public setDragMoveCallback(callback: (data: DragEventData) => void): void {
        this._onDragMove = callback;
    }

    public setDragEndCallback(callback: (data: DragEventData, success: boolean) => void): void {
        this._onDragEnd = callback;
    }

    /**
     * 设置网格大小
     */
    public setGridSize(size: number): void {
        this.gridSize = Math.max(1, size);
    }

    /**
     * 设置吸附阈值
     */
    public setSnapThreshold(threshold: number): void {
        this.snapThreshold = Math.max(0, threshold);
    }
}
