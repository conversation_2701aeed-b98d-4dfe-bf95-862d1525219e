import { _decorator, Component, Node, Canvas, UITransform, Widget, Label, Color, Sprite, SpriteFrame, ImageAsset, Texture2D } from 'cc';
import { GameManager } from './GameManager';
import { GameBoard } from './GameBoard';
import { GameUI } from './GameUI';
const { ccclass, property } = _decorator;

/**
 * 场景设置助手
 * 用于快速创建游戏场景的基本结构
 */
@ccclass('SceneSetup')
export class SceneSetup extends Component {

    @property
    public autoSetup: boolean = true;

    @property
    public boardSize: number = 6;

    @property
    public cellSize: number = 50;

    @property(GameUI)
    gameUI: GameUI = null;

    protected start() {
        if (this.autoSetup) {
            this.setupScene();
        }
    }

    /**
     * 自动设置场景
     */
    public setupScene(): void {
        console.log('Setting up Tetris Board Fill game scene...');

        // 获取或创建Canvas
       
        // 创建UI节点
        const uiNode = this.createUINode();

        // 创建游戏管理器节点
        const gameManagerNode = this.createGameManagerNode();
        uiNode.addChild(gameManagerNode);

        // 创建游戏棋盘节点
        const gameBoardNode = this.createGameBoardNode();
        uiNode.addChild(gameBoardNode);


        // 配置游戏管理器
        const gameManager = gameManagerNode.getComponent(GameManager);
        if (gameManager) {
            gameManager.gameBoardNode = gameBoardNode;
            gameManager.uiNode = uiNode;
            gameManager.boardSize = this.boardSize;
        }

        console.log('Scene setup completed!');
    }

    /**
     * 创建游戏管理器节点
     */
    private createGameManagerNode(): Node {
        const node = new Node('GameManager');
        const gameManager = node.addComponent(GameManager);
        gameManager.boardSize = this.boardSize;
        return node;
    }

    /**
     * 创建游戏棋盘节点
     */
    private createGameBoardNode(): Node {
        const node = new Node('GameBoard');

        // 添加UITransform组件
        const transform = node.addComponent(UITransform);
        transform.setContentSize(400, 400);

        // 添加GameBoard组件
        const gameBoard = node.addComponent(GameBoard);
        gameBoard.boardSize = this.boardSize;

        // 设置位置（屏幕中央偏左）
        node.setPosition(-100, 0, 0);

        return node;
    }

    /**
     * 创建UI节点
     */
    private createUINode(): Node {
        // 添加GameUI组件并配置
        const gameUI = this.gameUI
        const uiNode = gameUI.node

        // 创建棋盘容器
        const boardContainer = this.createBoardContainer();
        uiNode.addChild(boardContainer);

        // 创建方块队列容器
        const blockQueueContainer = this.createBlockQueueContainer();
        uiNode.addChild(blockQueueContainer);

        // 创建分数标签
        const scoreLabel = this.createScoreLabel();
        uiNode.addChild(scoreLabel);

        // 创建连击标签
        const comboLabel = this.createComboLabel();
        uiNode.addChild(comboLabel);

        // 创建行目标容器
        const rowTargetsContainer = this.createRowTargetsContainer();
        uiNode.addChild(rowTargetsContainer);

        // 创建列目标容器
        const colTargetsContainer = this.createColTargetsContainer();
        uiNode.addChild(colTargetsContainer);


        gameUI.boardContainer = boardContainer;
        gameUI.blockQueueContainer = blockQueueContainer;
        gameUI.scoreLabel = scoreLabel.getComponent(Label);
        gameUI.comboLabel = comboLabel.getComponent(Label);
        gameUI.rowTargetsContainer = rowTargetsContainer;
        gameUI.colTargetsContainer = colTargetsContainer;
        gameUI.cellSize = this.cellSize;
        // gameUI.node.setSiblingIndex()

        return uiNode;
    }

    /**
     * 创建棋盘容器
     */
    private createBoardContainer(): Node {
        const node = new Node('BoardContainer');
        const transform = node.addComponent(UITransform);
        transform.setContentSize(400, 400);
        node.setPosition(-100, 0, 0);
        return node;
    }

    /**
     * 创建方块队列容器
     */
    private createBlockQueueContainer(): Node {
        const node = new Node('BlockQueueContainer');
        const transform = node.addComponent(UITransform);
        transform.setContentSize(300, 100);
        node.setPosition(250, 200, 0);
        return node;
    }

    /**
     * 创建分数标签
     */
    private createScoreLabel(): Node {
        const node = new Node('ScoreLabel');
        const transform = node.addComponent(UITransform);
        transform.setContentSize(200, 50);

        const label = node.addComponent(Label);
        label.string = 'Score: 0';
        label.fontSize = 24;
        label.color = Color.WHITE;

        node.setPosition(250, 100, 0);
        return node;
    }

    /**
     * 创建连击标签
     */
    private createComboLabel(): Node {
        const node = new Node('ComboLabel');
        const transform = node.addComponent(UITransform);
        transform.setContentSize(200, 50);

        const label = node.addComponent(Label);
        label.string = '';
        label.fontSize = 20;
        label.color = Color.YELLOW;

        node.setPosition(250, 50, 0);
        return node;
    }

    /**
     * 创建行目标容器
     */
    private createRowTargetsContainer(): Node {
        const node = new Node('RowTargetsContainer');
        const transform = node.addComponent(UITransform);
        transform.setContentSize(100, 400);
        node.setPosition(-250, 0, 0);
        return node;
    }

    /**
     * 创建列目标容器
     */
    private createColTargetsContainer(): Node {
        const node = new Node('ColTargetsContainer');
        const transform = node.addComponent(UITransform);
        transform.setContentSize(400, 100);
        node.setPosition(-100, -250, 0);
        return node;
    }

    /**
     * 创建简单的白色纹理
     */
    private createWhiteTexture(): SpriteFrame {
        // 创建1x1的白色纹理
        const img = new ImageAsset();
        const texture = new Texture2D();
        texture.image = img;

        const spriteFrame = new SpriteFrame();
        spriteFrame.texture = texture;

        return spriteFrame;
    }

    /**
     * 手动设置场景（供编辑器调用）
     */
    public manualSetup(): void {
        this.setupScene();
    }

    /**
     * 清理场景
     */
    public cleanupScene(): void {
        // 移除所有子节点
        this.node.children.forEach(child => {
            if (child.name !== 'SceneSetup') {
                child.destroy();
            }
        });
        console.log('Scene cleaned up!');
    }
}
