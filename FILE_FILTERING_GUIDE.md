# 文件过滤指南

## 概述

在 Cocos Creator 项目中，会自动生成大量的 `.meta` 文件和其他辅助文件。这个指南说明如何过滤这些文件的显示，让项目结构更加清晰。

## 问题说明

### 原始目录结构（包含 .meta 文件）
```
assets/
├── scripts/
│   ├── TetrisBlock.ts
│   ├── TetrisBlock.ts.meta          ← 自动生成的元数据文件
│   ├── GameBoard.ts
│   ├── GameBoard.ts.meta            ← 自动生成的元数据文件
│   └── ...
├── scenes/
│   ├── game.scene
│   ├── game.scene.meta              ← 自动生成的元数据文件
│   └── ...
└── ...
```

### 清洁的目录结构（过滤后）
```
assets/
├── 📜 scripts/
│   ├── TetrisBlock.ts
│   ├── GameBoard.ts
│   ├── BlockGenerator.ts
│   └── ...
├── 🎬 scenes/
│   ├── game.scene
│   └── ...
└── ...
```

## 解决方案

### 1. FileUtils 工具类

创建了 `FileUtils.ts` 工具类来处理文件过滤：

```typescript
import { FileUtils } from './FileUtils';

// 过滤文件列表
const cleanFiles = FileUtils.filterFileList(allFiles);

// 检查文件类型
const isScript = FileUtils.isScriptFile('GameManager.ts');
const isScene = FileUtils.isSceneFile('main.scene');

// 生成清洁的目录结构
const structure = FileUtils.generateCleanDirectoryStructure('assets', files);
```

### 2. 过滤规则

#### 自动过滤的文件扩展名：
- `.meta` - Cocos Creator 元数据文件
- `.DS_Store` - macOS 系统文件
- `Thumbs.db` - Windows 缩略图文件
- `.gitkeep` - Git 保持空目录文件

#### 自动过滤的文件夹：
- `temp/` - 临时文件
- `library/` - 库文件
- `local/` - 本地配置
- `build/` - 构建输出
- `profiles/` - 配置文件
- `native/` - 原生代码
- `node_modules/` - NPM 依赖
- `.git/` - Git 仓库
- `.vscode/` - VSCode 配置
- `.idea/` - WebStorm 配置

### 3. ProjectStructureViewer 演示工具

创建了 `ProjectStructureViewer.ts` 来演示文件过滤功能：

```typescript
// 在任意节点添加 ProjectStructureViewer 组件
// 勾选 showOnStart 或调用以下方法：

viewer.manualShowStructure();           // 显示项目结构
viewer.manualDemonstrateFiltering();    // 演示过滤功能
viewer.manualDemonstrateFileTypes();    // 演示文件类型检测
```

## 使用方法

### 方法1：自动显示（推荐）
1. 在任意节点添加 `ProjectStructureViewer` 组件
2. 勾选 `showOnStart` 属性
3. 运行场景，查看控制台输出

### 方法2：手动调用
```typescript
import { FileUtils } from './FileUtils';

// 假设你有一个文件列表
const files = ['script.ts', 'script.ts.meta', 'scene.scene', 'scene.scene.meta'];

// 过滤文件
const cleanFiles = FileUtils.filterFileList(files);
console.log('清洁的文件列表:', cleanFiles);

// 生成结构
const structure = FileUtils.generateCleanDirectoryStructure('assets', files);
console.log(structure);
```

### 方法3：在编辑器中使用
1. 选择包含 `ProjectStructureViewer` 组件的节点
2. 在属性检查器中点击相应的方法按钮
3. 查看控制台输出

## 文件类型检测

FileUtils 支持检测多种文件类型：

### 脚本文件
- `.ts`, `.js`, `.tsx`, `.jsx`

### 场景文件
- `.scene`

### 预制体文件
- `.prefab`

### 资源文件
- **图片**: `.png`, `.jpg`, `.jpeg`, `.gif`, `.bmp`, `.tga`, `.webp`
- **音频**: `.mp3`, `.wav`, `.ogg`, `.m4a`
- **视频**: `.mp4`, `.avi`, `.mov`, `.webm`
- **字体**: `.ttf`, `.otf`, `.fnt`
- **数据**: `.json`, `.plist`, `.xml`
- **着色器**: `.shader`, `.effect`

## 重要提醒

### ✅ 应该做的：
1. 使用 FileUtils 过滤显示不必要的文件
2. 将 `.meta` 文件提交到版本控制系统
3. 使用 ProjectStructureViewer 查看清洁的项目结构
4. 在文档中忽略 `.meta` 文件的显示

### ❌ 不应该做的：
1. 删除 `.meta` 文件
2. 手动编辑 `.meta` 文件
3. 将 `.meta` 文件添加到 .gitignore
4. 在项目文档中列出 `.meta` 文件

## 版本控制

### .gitignore 配置
项目的 `.gitignore` 文件已正确配置：

```gitignore
# Cocos Creator 生成的文件
library/
temp/
local/
build/
profiles/
native

# 开发工具
node_modules/
.vscode/
.idea/
```

**注意**: `.meta` 文件不在 .gitignore 中，因为它们包含重要的资源元数据。

## 输出示例

使用 ProjectStructureViewer 的输出示例：

```
📂 Clean Directory Structure:
assets/
├── 📁 Folders:
│   ├── scripts
│   ├── scenes
│   ├── prefabs
│   └── textures
├── 📜 Scripts:
│   ├── scripts/TetrisBlock.ts
│   ├── scripts/GameBoard.ts
│   ├── scripts/BlockGenerator.ts
│   └── scripts/GameManager.ts
└── 🎬 Scenes:
    └── scenes/game.scene

📊 文件类型统计:
  📁 文件夹: 4
  📜 脚本文件: 10
  🎬 场景文件: 1
  🧩 预制体文件: 0
  🎨 资源文件: 0
  📄 其他文件: 0
  📋 总计: 15
```

## 总结

通过使用 FileUtils 和 ProjectStructureViewer，你可以：
1. 获得清洁的项目结构视图
2. 过滤掉不必要的 .meta 文件显示
3. 按文件类型组织和统计项目文件
4. 更好地理解项目结构

这些工具让项目管理更加高效，同时保持了 Cocos Creator 项目的完整性。
