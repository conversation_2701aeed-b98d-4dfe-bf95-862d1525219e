import { _decorator, Component, Node } from 'cc';
import { TetrisBlock, TetrisBlockData } from './TetrisBlock';
const { ccclass, property } = _decorator;

/**
 * 方块生成器类
 */
@ccclass('BlockGenerator')
export class BlockGenerator extends Component {
    
    @property
    public maxBlocksInQueue: number = 3;  // 最多同时存在的方块数量

    private _blockQueue: TetrisBlockData[] = [];
    private _isGenerating: boolean = false;

    protected onLoad() {
        this.fillBlockQueue();
    }

    /**
     * 填充方块队列
     */
    private fillBlockQueue(): void {
        while (this._blockQueue.length < this.maxBlocksInQueue) {
            const newBlock = TetrisBlock.createRandomBlock();
            this._blockQueue.push(newBlock);
        }
    }

    /**
     * 获取下一个方块
     */
    public getNextBlock(): TetrisBlockData | null {
        if (this._blockQueue.length === 0) {
            return null;
        }

        const block = this._blockQueue.shift();
        this.fillBlockQueue();  // 补充队列
        return block;
    }

    /**
     * 获取当前队列中的所有方块（用于预览）
     */
    public getBlockQueue(): TetrisBlockData[] {
        return [...this._blockQueue];  // 返回副本
    }

    /**
     * 获取指定索引的方块（不移除）
     */
    public getBlockAt(index: number): TetrisBlockData | null {
        if (index >= 0 && index < this._blockQueue.length) {
            return this._blockQueue[index];
        }
        return null;
    }

    /**
     * 移除指定索引的方块
     */
    public removeBlockAt(index: number): TetrisBlockData | null {
        if (index >= 0 && index < this._blockQueue.length) {
            const block = this._blockQueue.splice(index, 1)[0];
            this.fillBlockQueue();  // 补充队列
            return block;
        }
        return null;
    }

    /**
     * 旋转指定索引的方块
     */
    public rotateBlockAt(index: number): boolean {
        if (index >= 0 && index < this._blockQueue.length) {
            this._blockQueue[index] = TetrisBlock.rotateBlock(this._blockQueue[index]);
            return true;
        }
        return false;
    }

    /**
     * 清空队列并重新生成
     */
    public resetQueue(): void {
        this._blockQueue = [];
        this.fillBlockQueue();
    }

    /**
     * 获取队列长度
     */
    public getQueueLength(): number {
        return this._blockQueue.length;
    }

    /**
     * 检查队列是否为空
     */
    public isEmpty(): boolean {
        return this._blockQueue.length === 0;
    }

    /**
     * 检查队列是否已满
     */
    public isFull(): boolean {
        return this._blockQueue.length >= this.maxBlocksInQueue;
    }

    /**
     * 手动添加方块到队列
     */
    public addBlock(block: TetrisBlockData): boolean {
        if (this._blockQueue.length < this.maxBlocksInQueue) {
            this._blockQueue.push(block);
            return true;
        }
        return false;
    }

    /**
     * 设置最大队列长度
     */
    public setMaxBlocksInQueue(max: number): void {
        this.maxBlocksInQueue = Math.max(1, max);
        
        // 如果当前队列超过新的最大长度，则移除多余的方块
        while (this._blockQueue.length > this.maxBlocksInQueue) {
            this._blockQueue.pop();
        }
        
        // 如果队列不足，则补充
        this.fillBlockQueue();
    }

    /**
     * 获取生成状态
     */
    public isGenerating(): boolean {
        return this._isGenerating;
    }

    /**
     * 设置生成状态
     */
    public setGenerating(generating: boolean): void {
        this._isGenerating = generating;
    }
}
