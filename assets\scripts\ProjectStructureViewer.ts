import { _decorator, Component } from 'cc';
import { FileUtils } from './FileUtils';
const { ccclass, property } = _decorator;

/**
 * 项目结构查看器
 * 用于显示过滤后的清洁项目结构
 */
@ccclass('ProjectStructureViewer')
export class ProjectStructureViewer extends Component {
    
    @property
    public showOnStart: boolean = false;

    protected start() {
        if (this.showOnStart) {
            this.showProjectStructure();
        }
    }

    /**
     * 显示项目结构
     */
    public showProjectStructure(): void {
        console.log('=== 项目结构查看器 ===');
        
        // 模拟文件列表（在实际项目中，这些可能来自文件系统API）
        const assetsFiles = [
            'scripts',
            'scripts/TetrisBlock.ts',
            'scripts/TetrisBlock.ts.meta',
            'scripts/GameBoard.ts',
            'scripts/GameBoard.ts.meta',
            'scripts/BlockGenerator.ts',
            'scripts/BlockGenerator.ts.meta',
            'scripts/DragDropSystem.ts',
            'scripts/DragDropSystem.ts.meta',
            'scripts/ClearSystem.ts',
            'scripts/ClearSystem.ts.meta',
            'scripts/GameUI.ts',
            'scripts/GameUI.ts.meta',
            'scripts/GameManager.ts',
            'scripts/GameManager.ts.meta',
            'scripts/SceneSetup.ts',
            'scripts/SceneSetup.ts.meta',
            'scripts/GameTest.ts',
            'scripts/GameTest.ts.meta',
            'scripts/FileUtils.ts',
            'scripts/FileUtils.ts.meta',
            'scripts/ProjectStructureViewer.ts',
            'scripts/ProjectStructureViewer.ts.meta',
            'scenes',
            'scenes/game.scene',
            'scenes/game.scene.meta',
            'prefabs',
            'prefabs.meta',
            'textures',
            'textures.meta'
        ];

        // 显示原始文件列表
        console.log('📋 原始文件列表:');
        assetsFiles.forEach(file => console.log(`  ${file}`));
        
        console.log('\n' + '='.repeat(50) + '\n');
        
        // 显示过滤后的文件列表
        const filteredFiles = FileUtils.filterFileList(assetsFiles);
        console.log('🧹 过滤后的文件列表:');
        filteredFiles.forEach(file => console.log(`  ${file}`));
        
        console.log('\n' + '='.repeat(50) + '\n');
        
        // 显示格式化的目录结构
        const structure = FileUtils.generateCleanDirectoryStructure('assets', assetsFiles);
        console.log(structure);
        
        console.log('\n' + '='.repeat(50) + '\n');
        
        // 显示文件类型统计
        this.showFileTypeStatistics(filteredFiles);
    }

    /**
     * 显示文件类型统计
     */
    private showFileTypeStatistics(files: string[]): void {
        console.log('📊 文件类型统计:');
        
        let scriptCount = 0;
        let sceneCount = 0;
        let prefabCount = 0;
        let assetCount = 0;
        let folderCount = 0;
        let otherCount = 0;

        files.forEach(file => {
            const fileName = file.split(/[/\\]/).pop() || '';
            
            if (!fileName.includes('.')) {
                folderCount++;
            } else if (FileUtils.isScriptFile(fileName)) {
                scriptCount++;
            } else if (FileUtils.isSceneFile(fileName)) {
                sceneCount++;
            } else if (FileUtils.isPrefabFile(fileName)) {
                prefabCount++;
            } else if (FileUtils.isAssetFile(fileName)) {
                assetCount++;
            } else {
                otherCount++;
            }
        });

        console.log(`  📁 文件夹: ${folderCount}`);
        console.log(`  📜 脚本文件: ${scriptCount}`);
        console.log(`  🎬 场景文件: ${sceneCount}`);
        console.log(`  🧩 预制体文件: ${prefabCount}`);
        console.log(`  🎨 资源文件: ${assetCount}`);
        console.log(`  📄 其他文件: ${otherCount}`);
        console.log(`  📋 总计: ${files.length}`);
    }

    /**
     * 演示文件过滤功能
     */
    public demonstrateFiltering(): void {
        console.log('=== 文件过滤演示 ===');
        
        const testFiles = [
            'GameManager.ts',
            'GameManager.ts.meta',
            'main.scene',
            'main.scene.meta',
            'player.prefab',
            'player.prefab.meta',
            'texture.png',
            'texture.png.meta',
            '.DS_Store',
            'Thumbs.db',
            'temp',
            'library',
            'node_modules'
        ];

        console.log('🔍 测试文件列表:');
        testFiles.forEach(file => {
            const shouldFilter = FileUtils.shouldFilterFile(file) || FileUtils.shouldFilterFolder(file);
            const status = shouldFilter ? '❌ 过滤' : '✅ 保留';
            console.log(`  ${status} ${file}`);
        });

        console.log('\n📋 过滤后的文件:');
        const filtered = FileUtils.filterFileList(testFiles);
        filtered.forEach(file => console.log(`  ✅ ${file}`));
    }

    /**
     * 演示文件类型检测
     */
    public demonstrateFileTypeDetection(): void {
        console.log('=== 文件类型检测演示 ===');
        
        const testFiles = [
            'GameManager.ts',
            'main.scene',
            'player.prefab',
            'texture.png',
            'sound.mp3',
            'font.ttf',
            'data.json',
            'shader.effect',
            'unknown.xyz'
        ];

        testFiles.forEach(file => {
            console.log(`📄 ${file}:`);
            console.log(`  脚本文件: ${FileUtils.isScriptFile(file)}`);
            console.log(`  场景文件: ${FileUtils.isSceneFile(file)}`);
            console.log(`  预制体文件: ${FileUtils.isPrefabFile(file)}`);
            console.log(`  资源文件: ${FileUtils.isAssetFile(file)}`);
            console.log(`  扩展名: ${FileUtils.getFileExtension(file)}`);
            console.log(`  文件名(无扩展名): ${FileUtils.getFileNameWithoutExtension(file)}`);
            console.log('');
        });
    }

    /**
     * 手动显示项目结构（供编辑器调用）
     */
    public manualShowStructure(): void {
        this.showProjectStructure();
    }

    /**
     * 手动演示过滤功能（供编辑器调用）
     */
    public manualDemonstrateFiltering(): void {
        this.demonstrateFiltering();
    }

    /**
     * 手动演示文件类型检测（供编辑器调用）
     */
    public manualDemonstrateFileTypes(): void {
        this.demonstrateFileTypeDetection();
    }
}
