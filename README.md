# 俄罗斯方块填充棋盘游戏

这是一个基于 Cocos Creator 3.8.6 开发的俄罗斯方块填充棋盘游戏。

## 游戏规则

1. **棋盘设置**：创建一个 NxN 的棋盘（N > 3），每行每列都有随机生成的目标格子数
2. **方块放置**：使用最多3格的俄罗斯方块填充棋盘空位
3. **方块生成**：在棋盘外随机生成最多3格的俄罗斯方块，包含不同的旋转状态
4. **拖拽操作**：将随机生成的俄罗斯方块拖拽到棋盘某个位置放置
5. **放置检查**：只有当方块的所有格子都有空位时才能放置成功
6. **消除机制**：放置成功后，检查行列格子数是否满足目标数，满足则消除该行或列

## 项目结构

```
assets/
├── 📜 scripts/           # 游戏脚本
│   ├── TetrisBlock.ts      # 俄罗斯方块数据结构
│   ├── GameBoard.ts        # 游戏棋盘系统
│   ├── BlockGenerator.ts   # 方块生成系统
│   ├── DragDropSystem.ts   # 拖拽放置系统
│   ├── ClearSystem.ts      # 消除判断系统
│   ├── GameUI.ts          # 游戏UI管理
│   ├── GameManager.ts     # 游戏主管理器
│   ├── SceneSetup.ts      # 场景自动设置助手
│   ├── GameTest.ts        # 游戏逻辑测试
│   └── FileUtils.ts       # 文件工具类
├── 🎬 scenes/           # 游戏场景
├── 🧩 prefabs/          # 预制体
└── 🎨 textures/         # 贴图资源
```

> **注意**: Cocos Creator 会自动为每个文件生成对应的 `.meta` 文件，这些文件包含资源的元数据信息。在查看项目结构时，可以忽略这些 `.meta` 文件，它们不需要手动编辑。

## 核心系统说明

### 1. TetrisBlock（俄罗斯方块）
- 定义了7种经典俄罗斯方块类型（I、O、T、L、J、S、Z）
- 每种方块最多使用3个格子
- 支持4种旋转状态
- 包含颜色信息和位置数据

### 2. GameBoard（游戏棋盘）
- 管理 NxN 棋盘状态
- 随机生成每行每列的目标格子数
- 检查方块放置的有效性
- 处理行列消除逻辑

### 3. BlockGenerator（方块生成器）
- 维护方块队列（最多3个方块）
- 随机生成新方块
- 支持方块旋转操作
- 自动补充队列

### 4. DragDropSystem（拖拽系统）
- 处理方块的拖拽操作
- 实时检查放置位置的有效性
- 提供视觉反馈
- 支持拖拽取消

### 5. ClearSystem（消除系统）
- 检查行列是否满足目标数
- 执行消除动画
- 计算分数和连击
- 触发消除事件

### 6. GameUI（游戏界面）
- 显示棋盘和方块
- 显示目标数和当前进度
- 显示分数和连击数
- 更新方块队列显示

### 7. GameManager（游戏管理器）
- 协调所有系统工作
- 管理游戏状态
- 处理输入事件
- 控制游戏流程

## 使用方法

### 1. 在 Cocos Creator 中设置

1. 打开 Cocos Creator 3.8.6
2. 创建新场景或使用现有场景
3. 创建以下节点结构：
   ```
   Canvas
   ├── GameManager (添加 GameManager 组件)
   ├── GameBoard (添加 GameBoard 组件)
   └── UI
       ├── BoardContainer
       ├── BlockQueueContainer
       ├── ScoreLabel
       ├── ComboLabel
       ├── RowTargetsContainer
       └── ColTargetsContainer
   ```

### 2. 组件配置

#### GameManager 配置：
- `gameBoardNode`: 指向 GameBoard 节点
- `uiNode`: 指向 UI 节点
- `boardSize`: 棋盘大小（默认6）
- `maxBlocksInQueue`: 最大方块队列数（默认3）

#### GameBoard 配置：
- `boardSize`: 棋盘大小

#### GameUI 配置：
- `boardContainer`: 棋盘容器节点
- `blockQueueContainer`: 方块队列容器节点
- `scoreLabel`: 分数标签
- `comboLabel`: 连击标签
- `rowTargetsContainer`: 行目标容器
- `colTargetsContainer`: 列目标容器
- `cellSize`: 格子大小（默认50）

### 3. 预制体创建

建议创建以下预制体：
- `CellPrefab`: 棋盘格子预制体
- `BlockPrefab`: 方块预制体
- `TargetLabelPrefab`: 目标数标签预制体

## 扩展功能

### 可以添加的功能：
1. **音效系统**：添加放置、消除、失败等音效
2. **粒子效果**：消除时的特效
3. **关卡系统**：不同难度的关卡
4. **道具系统**：特殊道具帮助玩家
5. **存档系统**：保存游戏进度
6. **排行榜**：记录最高分数

### 自定义方块：
可以在 `TetrisBlock.ts` 中的 `BLOCK_SHAPES` 添加新的方块形状。

### 调整难度：
- 修改棋盘大小
- 调整目标数生成范围
- 改变方块队列数量
- 调整分数计算公式

## 文件管理

### .meta 文件说明
Cocos Creator 会为每个资源文件自动生成对应的 `.meta` 文件，这些文件包含：
- 资源的 UUID
- 导入设置
- 子资源信息
- 其他元数据

**重要提示**:
- ✅ `.meta` 文件应该提交到版本控制系统
- ❌ 不要手动编辑 `.meta` 文件
- ❌ 不要删除 `.meta` 文件
- ✅ 在查看项目结构时可以忽略这些文件

### 使用 FileUtils 过滤显示
项目中包含了 `FileUtils.ts` 工具类，可以帮助过滤不必要的文件显示：

```typescript
// 过滤文件列表
const cleanFiles = FileUtils.filterFileList(allFiles);

// 生成清洁的目录结构
const structure = FileUtils.generateCleanDirectoryStructure('assets', files);

// 检查文件类型
const isScript = FileUtils.isScriptFile('GameManager.ts');
const isScene = FileUtils.isSceneFile('main.scene');
```

## 注意事项

1. 确保所有节点引用正确设置
2. 预制体需要正确配置组件
3. 触摸事件需要在相应节点上启用
4. 建议在真机上测试拖拽功能
5. 可以根据需要调整UI布局和样式
6. 不要删除或修改 `.meta` 文件

## 开发环境

- Cocos Creator 3.8.6
- TypeScript
- 支持 2D 渲染管线

这个游戏框架提供了完整的俄罗斯方块填充棋盘玩法，可以根据具体需求进行扩展和定制。
