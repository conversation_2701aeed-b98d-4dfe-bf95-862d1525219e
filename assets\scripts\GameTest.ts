import { _decorator, Component, Node } from 'cc';
import { TetrisBlock, TetrisBlockType, RotationState } from './TetrisBlock';
import { GameBoard } from './GameBoard';
import { BlockGenerator } from './BlockGenerator';
import { ClearSystem } from './ClearSystem';
const { ccclass, property } = _decorator;

/**
 * 游戏测试类
 * 用于测试游戏核心逻辑
 */
@ccclass('GameTest')
export class GameTest extends Component {
    
    @property
    public runTestsOnStart: boolean = false;

    protected start() {
        if (this.runTestsOnStart) {
            this.runAllTests();
        }
    }

    /**
     * 运行所有测试
     */
    public runAllTests(): void {
        console.log('=== Starting Game Tests ===');
        
        this.testTetrisBlock();
        this.testGameBoard();
        this.testBlockGenerator();
        this.testClearSystem();
        
        console.log('=== All Tests Completed ===');
    }

    /**
     * 测试俄罗斯方块
     */
    private testTetrisBlock(): void {
        console.log('Testing TetrisBlock...');
        
        // 测试创建随机方块
        const randomBlock = TetrisBlock.createRandomBlock();
        console.log('Random block created:', randomBlock);
        
        // 测试方块旋转
        const rotatedBlock = TetrisBlock.rotateBlock(randomBlock);
        console.log('Block rotated:', rotatedBlock);
        
        // 测试获取边界框
        const boundingBox = TetrisBlock.getBoundingBox(randomBlock.positions);
        console.log('Bounding box:', boundingBox);
        
        // 测试所有方块类型
        Object.values(TetrisBlockType).forEach(type => {
            const positions = TetrisBlock.getBlockPositions(type, RotationState.ROTATION_0);
            console.log(`${type} block positions:`, positions);
        });
        
        console.log('TetrisBlock tests passed!');
    }

    /**
     * 测试游戏棋盘
     */
    private testGameBoard(): void {
        console.log('Testing GameBoard...');
        
        // 创建测试棋盘
        const boardNode = new Node('TestBoard');
        const gameBoard = boardNode.addComponent(GameBoard);
        gameBoard.boardSize = 5;
        
        // 测试棋盘初始化
        const boardState = gameBoard.getBoardState();
        console.log('Board size:', gameBoard.getBoardSize());
        console.log('Board initialized, empty cells:', boardState.length * boardState[0].length);
        
        // 测试目标数生成
        const targets = gameBoard.getTargets();
        console.log('Row targets:', targets.rowTargets);
        console.log('Col targets:', targets.colTargets);
        
        // 测试方块放置
        const testBlock = TetrisBlock.createRandomBlock();
        const canPlace = gameBoard.canPlaceBlock(testBlock, 2, 2);
        console.log('Can place block at (2,2):', canPlace);
        
        if (canPlace) {
            const placed = gameBoard.placeBlock(testBlock, 2, 2);
            console.log('Block placed successfully:', placed);
            
            // 测试行列计数
            const rowCount = gameBoard.getRowFilledCount(2);
            const colCount = gameBoard.getColFilledCount(2);
            console.log('Row 2 filled count:', rowCount);
            console.log('Col 2 filled count:', colCount);
        }
        
        // 测试消除检查
        const clearResult = gameBoard.checkAndClearLines();
        console.log('Clear result:', clearResult);
        
        // 清理
        boardNode.destroy();
        console.log('GameBoard tests passed!');
    }

    /**
     * 测试方块生成器
     */
    private testBlockGenerator(): void {
        console.log('Testing BlockGenerator...');
        
        // 创建测试生成器
        const generatorNode = new Node('TestGenerator');
        const generator = generatorNode.addComponent(BlockGenerator);
        generator.maxBlocksInQueue = 3;
        
        // 测试队列初始化
        console.log('Initial queue length:', generator.getQueueLength());
        console.log('Queue is full:', generator.isFull());
        
        // 测试获取方块
        const block1 = generator.getNextBlock();
        console.log('Got block from queue:', block1);
        console.log('Queue length after getting block:', generator.getQueueLength());
        
        // 测试方块旋转
        const rotated = generator.rotateBlockAt(0);
        console.log('Rotated block at index 0:', rotated);
        
        // 测试移除方块
        const removedBlock = generator.removeBlockAt(0);
        console.log('Removed block:', removedBlock);
        console.log('Queue length after removal:', generator.getQueueLength());
        
        // 测试重置队列
        generator.resetQueue();
        console.log('Queue length after reset:', generator.getQueueLength());
        
        // 清理
        generatorNode.destroy();
        console.log('BlockGenerator tests passed!');
    }

    /**
     * 测试消除系统
     */
    private testClearSystem(): void {
        console.log('Testing ClearSystem...');
        
        // 创建测试棋盘和消除系统
        const boardNode = new Node('TestBoard');
        const gameBoard = boardNode.addComponent(GameBoard);
        const clearSystem = boardNode.addComponent(ClearSystem);
        gameBoard.boardSize = 4;
        
        // 设置测试回调
        clearSystem.setClearStartCallback((result) => {
            console.log('Clear started callback:', result);
        });
        
        clearSystem.setClearCompleteCallback((result) => {
            console.log('Clear completed callback:', result);
        });
        
        clearSystem.setScoreUpdateCallback((score, combo) => {
            console.log('Score updated:', score, 'Combo:', combo);
        });
        
        // 测试分数计算
        console.log('Initial score:', clearSystem.getTotalScore());
        console.log('Initial combo:', clearSystem.getCurrentCombo());
        
        // 模拟放置一些方块来测试消除
        const testBlock = TetrisBlock.createRandomBlock();
        if (gameBoard.canPlaceBlock(testBlock, 1, 1)) {
            gameBoard.placeBlock(testBlock, 1, 1);
            
            // 测试消除检查
            const clearResult = clearSystem.checkAndClear();
            console.log('Clear result:', clearResult);
        }
        
        // 测试行列消除条件检查
        const canClearRow = clearSystem.checkRowClearCondition(0);
        const canClearCol = clearSystem.checkColClearCondition(0);
        console.log('Can clear row 0:', canClearRow);
        console.log('Can clear col 0:', canClearCol);
        
        // 测试获取可消除的行列
        const clearableLines = clearSystem.getClearableLines();
        console.log('Clearable lines:', clearableLines);
        
        // 测试重置分数
        clearSystem.resetScore();
        console.log('Score after reset:', clearSystem.getTotalScore());
        
        // 清理
        boardNode.destroy();
        console.log('ClearSystem tests passed!');
    }

    /**
     * 性能测试
     */
    public performanceTest(): void {
        console.log('=== Performance Test ===');
        
        const startTime = Date.now();
        
        // 创建大量方块进行性能测试
        for (let i = 0; i < 1000; i++) {
            const block = TetrisBlock.createRandomBlock();
            TetrisBlock.rotateBlock(block);
            TetrisBlock.getBoundingBox(block.positions);
        }
        
        const endTime = Date.now();
        console.log(`Created and processed 1000 blocks in ${endTime - startTime}ms`);
        
        // 测试棋盘操作性能
        const boardNode = new Node('PerfTestBoard');
        const gameBoard = boardNode.addComponent(GameBoard);
        gameBoard.boardSize = 10;
        
        const boardStartTime = Date.now();
        
        for (let i = 0; i < 100; i++) {
            const block = TetrisBlock.createRandomBlock();
            const x = Math.floor(Math.random() * 8);
            const y = Math.floor(Math.random() * 8);
            
            if (gameBoard.canPlaceBlock(block, x, y)) {
                gameBoard.placeBlock(block, x, y);
            }
        }
        
        const boardEndTime = Date.now();
        console.log(`Performed 100 board operations in ${boardEndTime - boardStartTime}ms`);
        
        boardNode.destroy();
        console.log('=== Performance Test Completed ===');
    }

    /**
     * 手动运行测试（供编辑器调用）
     */
    public manualRunTests(): void {
        this.runAllTests();
    }

    /**
     * 运行性能测试（供编辑器调用）
     */
    public manualPerformanceTest(): void {
        this.performanceTest();
    }
}
