# 快速开始指南

## 1. 基本设置

### 在 Cocos Creator 中创建场景：

1. 打开 Cocos Creator 3.8.6
2. 创建新场景或打开现有场景
3. 在场景中创建以下节点结构：

```
Canvas
├── SceneSetup (添加 SceneSetup 组件)
└── (其他节点将自动创建)
```

### 自动设置：

1. 选择 Canvas 节点
2. 添加 `SceneSetup` 组件
3. 在 SceneSetup 组件中：
   - 勾选 `autoSetup`
   - 设置 `boardSize` (推荐 6)
   - 设置 `cellSize` (推荐 50)
4. 运行场景，系统会自动创建所有必要的节点和组件

## 2. 手动设置（可选）

如果不使用自动设置，可以手动创建以下结构：

```
Canvas
├── GameManager (GameManager 组件)
├── GameBoard (GameBoard 组件)
└── UI (GameUI 组件)
    ├── BoardContainer
    ├── BlockQueueContainer
    ├── ScoreLabel (Label 组件)
    ├── ComboLabel (Label 组件)
    ├── RowTargetsContainer
    └── ColTargetsContainer
```

### 组件配置：

#### GameManager：
- `gameBoardNode`: 拖入 GameBoard 节点
- `uiNode`: 拖入 UI 节点
- `boardSize`: 6
- `maxBlocksInQueue`: 3

#### GameBoard：
- `boardSize`: 6

#### GameUI：
- `boardContainer`: 拖入 BoardContainer 节点
- `blockQueueContainer`: 拖入 BlockQueueContainer 节点
- `scoreLabel`: 拖入 ScoreLabel 的 Label 组件
- `comboLabel`: 拖入 ComboLabel 的 Label 组件
- `rowTargetsContainer`: 拖入 RowTargetsContainer 节点
- `colTargetsContainer`: 拖入 ColTargetsContainer 节点
- `cellSize`: 50

## 3. 测试游戏

### 运行测试：

1. 在任意节点添加 `GameTest` 组件
2. 勾选 `runTestsOnStart`
3. 运行场景查看控制台输出

### 测试内容：
- 俄罗斯方块创建和旋转
- 棋盘初始化和方块放置
- 方块生成器队列管理
- 消除系统逻辑

## 4. 游戏玩法

### 基本操作：
1. 游戏开始时会显示一个 6x6 的棋盘
2. 每行每列都有随机生成的目标格子数
3. 右侧显示待放置的方块队列（最多3个）
4. 拖拽方块到棋盘上进行放置
5. 当行或列的填充数达到目标数时会自动消除
6. 消除会获得分数和连击

### 目标：
- 尽可能多地消除行列
- 获得更高的分数
- 保持连击以获得分数倍数

## 5. 自定义配置

### 修改棋盘大小：
在 GameManager 或 GameBoard 组件中修改 `boardSize` 属性

### 修改方块队列数量：
在 GameManager 或 BlockGenerator 组件中修改 `maxBlocksInQueue` 属性

### 修改分数系统：
在 ClearSystem 组件中修改：
- `scorePerCell`: 每个格子的基础分数
- `comboMultiplier`: 连击倍数

### 添加新方块类型：
在 `TetrisBlock.ts` 的 `BLOCK_SHAPES` 中添加新的方块定义

## 6. 常见问题

### Q: 拖拽不工作？
A: 确保：
1. DragDropSystem 组件正确配置
2. 相关节点启用了触摸事件
3. Camera 组件正确设置

### Q: 方块无法放置？
A: 检查：
1. 目标位置是否有空位
2. 方块是否超出棋盘边界
3. GameBoard 组件是否正确初始化

### Q: 消除不工作？
A: 确认：
1. ClearSystem 组件已添加到正确节点
2. 行列填充数确实达到了目标数
3. 目标数生成是否正确

### Q: UI 显示异常？
A: 检查：
1. 所有 UI 节点引用是否正确设置
2. UITransform 组件是否正确配置
3. 节点层级结构是否正确

## 7. 扩展建议

### 视觉效果：
- 添加方块放置动画
- 添加消除特效
- 改善UI样式

### 游戏功能：
- 添加暂停/恢复功能
- 实现存档系统
- 添加关卡系统
- 实现排行榜

### 音效：
- 方块放置音效
- 消除音效
- 背景音乐

这个快速开始指南应该能帮助你在几分钟内运行起俄罗斯方块填充棋盘游戏！
