import { _decorator, Component, Vec2 } from 'cc';
import { TetrisBlockData } from './TetrisBlock';
const { ccclass, property } = _decorator;

/**
 * 棋盘格子状态
 */
export interface CellState {
    filled: boolean;    // 是否被填充
    color: string;      // 格子颜色
}

/**
 * 行列目标数据
 */
export interface TargetData {
    rowTargets: number[];    // 每行的目标格子数
    colTargets: number[];    // 每列的目标格子数
}

/**
 * 游戏棋盘类
 */
@ccclass('GameBoard')
export class GameBoard extends Component {
    
    @property
    public boardSize: number = 6;  // 棋盘大小，默认6x6

    private _board: CellState[][];
    private _targets: TargetData;

    protected onLoad() {
        this.initializeBoard();
        this.generateTargets();
    }

    /**
     * 初始化棋盘
     */
    private initializeBoard(): void {
        this._board = [];
        for (let row = 0; row < this.boardSize; row++) {
            this._board[row] = [];
            for (let col = 0; col < this.boardSize; col++) {
                this._board[row][col] = {
                    filled: false,
                    color: '#FFFFFF'
                };
            }
        }
    }

    /**
     * 生成随机目标数
     */
    private generateTargets(): void {
        this._targets = {
            rowTargets: [],
            colTargets: []
        };

        // 为每行生成目标数（1到boardSize-1之间）
        for (let i = 0; i < this.boardSize; i++) {
            this._targets.rowTargets[i] = Math.floor(Math.random() * (this.boardSize - 1)) + 1;
        }

        // 为每列生成目标数（1到boardSize-1之间）
        for (let i = 0; i < this.boardSize; i++) {
            this._targets.colTargets[i] = Math.floor(Math.random() * (this.boardSize - 1)) + 1;
        }
    }

    /**
     * 检查指定位置是否可以放置方块
     */
    public canPlaceBlock(blockData: TetrisBlockData, centerX: number, centerY: number): boolean {
        for (const pos of blockData.positions) {
            const x = centerX + pos.x;
            const y = centerY + pos.y;
            
            // 检查是否超出边界
            if (x < 0 || x >= this.boardSize || y < 0 || y >= this.boardSize) {
                return false;
            }
            
            // 检查位置是否已被占用
            if (this._board[y][x].filled) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 在指定位置放置方块
     */
    public placeBlock(blockData: TetrisBlockData, centerX: number, centerY: number): boolean {
        if (!this.canPlaceBlock(blockData, centerX, centerY)) {
            return false;
        }

        // 放置方块
        for (const pos of blockData.positions) {
            const x = centerX + pos.x;
            const y = centerY + pos.y;
            
            this._board[y][x].filled = true;
            this._board[y][x].color = blockData.color;
        }

        return true;
    }

    /**
     * 检查并消除满足条件的行列
     */
    public checkAndClearLines(): { clearedRows: number[], clearedCols: number[] } {
        const clearedRows: number[] = [];
        const clearedCols: number[] = [];

        // 检查行
        for (let row = 0; row < this.boardSize; row++) {
            const filledCount = this.getRowFilledCount(row);
            if (filledCount === this._targets.rowTargets[row]) {
                clearedRows.push(row);
                this.clearRow(row);
            }
        }

        // 检查列
        for (let col = 0; col < this.boardSize; col++) {
            const filledCount = this.getColFilledCount(col);
            if (filledCount === this._targets.colTargets[col]) {
                clearedCols.push(col);
                this.clearCol(col);
            }
        }

        return { clearedRows, clearedCols };
    }

    /**
     * 获取指定行的填充格子数
     */
    public getRowFilledCount(row: number): number {
        let count = 0;
        for (let col = 0; col < this.boardSize; col++) {
            if (this._board[row][col].filled) {
                count++;
            }
        }
        return count;
    }

    /**
     * 获取指定列的填充格子数
     */
    public getColFilledCount(col: number): number {
        let count = 0;
        for (let row = 0; row < this.boardSize; row++) {
            if (this._board[row][col].filled) {
                count++;
            }
        }
        return count;
    }

    /**
     * 清除指定行
     */
    private clearRow(row: number): void {
        for (let col = 0; col < this.boardSize; col++) {
            this._board[row][col].filled = false;
            this._board[row][col].color = '#FFFFFF';
        }
    }

    /**
     * 清除指定列
     */
    private clearCol(col: number): void {
        for (let row = 0; row < this.boardSize; row++) {
            this._board[row][col].filled = false;
            this._board[row][col].color = '#FFFFFF';
        }
    }

    /**
     * 获取棋盘状态
     */
    public getBoardState(): CellState[][] {
        return this._board;
    }

    /**
     * 获取目标数据
     */
    public getTargets(): TargetData {
        return this._targets;
    }

    /**
     * 重置棋盘
     */
    public resetBoard(): void {
        this.initializeBoard();
        this.generateTargets();
    }

    /**
     * 检查游戏是否结束（棋盘是否已满）
     */
    public isGameOver(): boolean {
        for (let row = 0; row < this.boardSize; row++) {
            for (let col = 0; col < this.boardSize; col++) {
                if (!this._board[row][col].filled) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 获取棋盘大小
     */
    public getBoardSize(): number {
        return this.boardSize;
    }

    /**
     * 设置棋盘大小（需要重新初始化）
     */
    public setBoardSize(size: number): void {
        if (size > 3) {
            this.boardSize = size;
            this.initializeBoard();
            this.generateTargets();
        }
    }
}
