import { _decorator, Component } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 文件工具类
 * 提供文件过滤和目录管理功能
 */
@ccclass('FileUtils')
export class FileUtils extends Component {
    
    /**
     * 过滤掉不需要显示的文件扩展名
     */
    private static readonly FILTERED_EXTENSIONS = [
        '.meta',        // Cocos Creator 元数据文件
        '.DS_Store',    // macOS 系统文件
        'Thumbs.db',    // Windows 缩略图文件
        '.gitkeep',     // Git 保持空目录文件
        '.gitignore'    // Git 忽略文件（在某些情况下）
    ];

    /**
     * 过滤掉不需要显示的文件夹
     */
    private static readonly FILTERED_FOLDERS = [
        'temp',
        'library',
        'local',
        'build',
        'profiles',
        'native',
        'node_modules',
        '.git',
        '.vscode',
        '.idea'
    ];

    /**
     * 检查文件是否应该被过滤
     */
    public static shouldFilterFile(fileName: string): boolean {
        // 检查文件扩展名
        for (const ext of this.FILTERED_EXTENSIONS) {
            if (fileName.toLowerCase().endsWith(ext.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查文件夹是否应该被过滤
     */
    public static shouldFilterFolder(folderName: string): boolean {
        const lowerName = folderName.toLowerCase();
        return this.FILTERED_FOLDERS.some(filtered => 
            lowerName === filtered.toLowerCase()
        );
    }

    /**
     * 过滤文件列表
     */
    public static filterFileList(files: string[]): string[] {
        return files.filter(file => {
            // 提取文件名（去掉路径）
            const fileName = file.split(/[/\\]/).pop() || '';
            
            // 检查是否是文件夹（没有扩展名）
            const isFolder = !fileName.includes('.');
            
            if (isFolder) {
                return !this.shouldFilterFolder(fileName);
            } else {
                return !this.shouldFilterFile(fileName);
            }
        });
    }

    /**
     * 格式化文件路径显示
     */
    public static formatPathForDisplay(path: string): string {
        // 移除 .meta 文件
        if (path.endsWith('.meta')) {
            return '';
        }
        
        // 标准化路径分隔符
        return path.replace(/\\/g, '/');
    }

    /**
     * 获取文件扩展名
     */
    public static getFileExtension(fileName: string): string {
        const lastDot = fileName.lastIndexOf('.');
        return lastDot > 0 ? fileName.substring(lastDot) : '';
    }

    /**
     * 获取不带扩展名的文件名
     */
    public static getFileNameWithoutExtension(fileName: string): string {
        const lastDot = fileName.lastIndexOf('.');
        const lastSlash = Math.max(fileName.lastIndexOf('/'), fileName.lastIndexOf('\\'));
        const nameStart = lastSlash + 1;
        
        if (lastDot > nameStart) {
            return fileName.substring(nameStart, lastDot);
        } else {
            return fileName.substring(nameStart);
        }
    }

    /**
     * 检查是否是脚本文件
     */
    public static isScriptFile(fileName: string): boolean {
        const scriptExtensions = ['.ts', '.js', '.tsx', '.jsx'];
        const ext = this.getFileExtension(fileName).toLowerCase();
        return scriptExtensions.indexOf(ext) >= 0;
    }

    /**
     * 检查是否是场景文件
     */
    public static isSceneFile(fileName: string): boolean {
        return fileName.toLowerCase().endsWith('.scene');
    }

    /**
     * 检查是否是预制体文件
     */
    public static isPrefabFile(fileName: string): boolean {
        return fileName.toLowerCase().endsWith('.prefab');
    }

    /**
     * 检查是否是资源文件
     */
    public static isAssetFile(fileName: string): boolean {
        const assetExtensions = [
            '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tga', '.webp',  // 图片
            '.mp3', '.wav', '.ogg', '.m4a',                           // 音频
            '.mp4', '.avi', '.mov', '.webm',                          // 视频
            '.ttf', '.otf', '.fnt',                                   // 字体
            '.json', '.plist', '.xml',                                // 数据
            '.shader', '.effect'                                      // 着色器
        ];
        const ext = this.getFileExtension(fileName).toLowerCase();
        return assetExtensions.indexOf(ext) >= 0;
    }

    /**
     * 生成过滤后的目录结构字符串
     */
    public static generateCleanDirectoryStructure(basePath: string, files: string[]): string {
        const filteredFiles = this.filterFileList(files);
        const structure: string[] = [];
        
        // 按类型分组
        const folders: string[] = [];
        const scripts: string[] = [];
        const scenes: string[] = [];
        const prefabs: string[] = [];
        const assets: string[] = [];
        const others: string[] = [];

        filteredFiles.forEach(file => {
            const formattedPath = this.formatPathForDisplay(file);
            if (!formattedPath) return;

            const fileName = file.split(/[/\\]/).pop() || '';
            
            if (!fileName.includes('.')) {
                folders.push(formattedPath);
            } else if (this.isScriptFile(fileName)) {
                scripts.push(formattedPath);
            } else if (this.isSceneFile(fileName)) {
                scenes.push(formattedPath);
            } else if (this.isPrefabFile(fileName)) {
                prefabs.push(formattedPath);
            } else if (this.isAssetFile(fileName)) {
                assets.push(formattedPath);
            } else {
                others.push(formattedPath);
            }
        });

        // 生成结构字符串
        structure.push(`${basePath}/`);
        
        if (folders.length > 0) {
            structure.push('├── 📁 Folders:');
            folders.forEach(folder => structure.push(`│   ├── ${folder}`));
        }
        
        if (scripts.length > 0) {
            structure.push('├── 📜 Scripts:');
            scripts.forEach(script => structure.push(`│   ├── ${script}`));
        }
        
        if (scenes.length > 0) {
            structure.push('├── 🎬 Scenes:');
            scenes.forEach(scene => structure.push(`│   ├── ${scene}`));
        }
        
        if (prefabs.length > 0) {
            structure.push('├── 🧩 Prefabs:');
            prefabs.forEach(prefab => structure.push(`│   ├── ${prefab}`));
        }
        
        if (assets.length > 0) {
            structure.push('├── 🎨 Assets:');
            assets.forEach(asset => structure.push(`│   ├── ${asset}`));
        }
        
        if (others.length > 0) {
            structure.push('└── 📄 Others:');
            others.forEach(other => structure.push(`    ├── ${other}`));
        }

        return structure.join('\n');
    }

    /**
     * 打印过滤后的目录结构
     */
    public static logCleanDirectoryStructure(basePath: string, files: string[]): void {
        const structure = this.generateCleanDirectoryStructure(basePath, files);
        console.log('📂 Clean Directory Structure:');
        console.log(structure);
    }
}
