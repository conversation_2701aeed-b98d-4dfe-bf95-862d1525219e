import { _decorator, Component, Vec2 } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 俄罗斯方块形状枚举
 */
export enum TetrisBlockType {
    I = 'I',    // 直线型
    O = 'O',    // 正方形
    T = 'T',    // T型
    L = 'L',    // L型
    J = 'J',    // J型
    S = 'S',    // S型
    Z = 'Z'     // Z型
}

/**
 * 俄罗斯方块旋转状态
 */
export enum RotationState {
    ROTATION_0 = 0,
    ROTATION_90 = 1,
    ROTATION_180 = 2,
    ROTATION_270 = 3
}

/**
 * 俄罗斯方块数据结构
 */
export interface TetrisBlockData {
    type: TetrisBlockType;
    rotation: RotationState;
    positions: Vec2[];  // 相对于中心点的位置
    color: string;      // 方块颜色
}

/**
 * 俄罗斯方块定义类
 */
@ccclass('TetrisBlock')
export class TetrisBlock extends Component {
    
    // 定义所有俄罗斯方块的基本形状（最多3格，所以只使用部分经典形状）
    private static readonly BLOCK_SHAPES: { [key in TetrisBlockType]: Vec2[][] } = {
        [TetrisBlockType.I]: [
            // 3格直线型
            [new Vec2(-1, 0), new Vec2(0, 0), new Vec2(1, 0)],  // 水平
            [new Vec2(0, -1), new Vec2(0, 0), new Vec2(0, 1)],  // 垂直
            [new Vec2(-1, 0), new Vec2(0, 0), new Vec2(1, 0)],  // 水平
            [new Vec2(0, -1), new Vec2(0, 0), new Vec2(0, 1)]   // 垂直
        ],
        [TetrisBlockType.O]: [
            // 2x2正方形（只取3格）
            [new Vec2(0, 0), new Vec2(1, 0), new Vec2(0, 1)],
            [new Vec2(0, 0), new Vec2(1, 0), new Vec2(0, 1)],
            [new Vec2(0, 0), new Vec2(1, 0), new Vec2(0, 1)],
            [new Vec2(0, 0), new Vec2(1, 0), new Vec2(0, 1)]
        ],
        [TetrisBlockType.T]: [
            // T型（3格）
            [new Vec2(-1, 0), new Vec2(0, 0), new Vec2(0, 1)],  // ┴
            [new Vec2(0, -1), new Vec2(0, 0), new Vec2(1, 0)],  // ├
            [new Vec2(0, -1), new Vec2(-1, 0), new Vec2(0, 0)], // ┬
            [new Vec2(-1, 0), new Vec2(0, 0), new Vec2(0, 1)]   // ┤
        ],
        [TetrisBlockType.L]: [
            // L型（3格）
            [new Vec2(-1, 0), new Vec2(0, 0), new Vec2(0, 1)],
            [new Vec2(0, -1), new Vec2(0, 0), new Vec2(1, 0)],
            [new Vec2(0, -1), new Vec2(0, 0), new Vec2(1, 0)],
            [new Vec2(-1, 0), new Vec2(0, 0), new Vec2(0, 1)]
        ],
        [TetrisBlockType.J]: [
            // J型（3格）
            [new Vec2(0, 0), new Vec2(1, 0), new Vec2(0, 1)],
            [new Vec2(0, -1), new Vec2(0, 0), new Vec2(1, 0)],
            [new Vec2(0, -1), new Vec2(-1, 0), new Vec2(0, 0)],
            [new Vec2(-1, 0), new Vec2(0, 0), new Vec2(0, 1)]
        ],
        [TetrisBlockType.S]: [
            // S型（3格）
            [new Vec2(0, 0), new Vec2(1, 0), new Vec2(0, 1)],
            [new Vec2(0, -1), new Vec2(0, 0), new Vec2(1, 0)],
            [new Vec2(0, 0), new Vec2(1, 0), new Vec2(0, 1)],
            [new Vec2(0, -1), new Vec2(0, 0), new Vec2(1, 0)]
        ],
        [TetrisBlockType.Z]: [
            // Z型（3格）
            [new Vec2(-1, 0), new Vec2(0, 0), new Vec2(0, 1)],
            [new Vec2(0, -1), new Vec2(0, 0), new Vec2(-1, 0)],
            [new Vec2(-1, 0), new Vec2(0, 0), new Vec2(0, 1)],
            [new Vec2(0, -1), new Vec2(0, 0), new Vec2(-1, 0)]
        ]
    };

    // 方块颜色定义
    private static readonly BLOCK_COLORS: { [key in TetrisBlockType]: string } = {
        [TetrisBlockType.I]: '#00FFFF',  // 青色
        [TetrisBlockType.O]: '#FFFF00',  // 黄色
        [TetrisBlockType.T]: '#800080',  // 紫色
        [TetrisBlockType.L]: '#FFA500',  // 橙色
        [TetrisBlockType.J]: '#0000FF',  // 蓝色
        [TetrisBlockType.S]: '#00FF00',  // 绿色
        [TetrisBlockType.Z]: '#FF0000'   // 红色
    };

    private _blockData: TetrisBlockData;

    /**
     * 创建一个随机的俄罗斯方块
     */
    public static createRandomBlock(): TetrisBlockData {

        const keys = Object.keys(TetrisBlockType);
        const randomKey = keys[Math.floor(Math.random() * keys.length)];
        const randomRotation = Math.floor(Math.random() * 4) as RotationState;
        const randomType = TetrisBlockType[randomKey];
        return {
            type: randomType,
            rotation: randomRotation,
            positions: this.getBlockPositions(randomType, randomRotation),
            color: this.BLOCK_COLORS[randomType]
        };
    }

    /**
     * 获取指定类型和旋转状态的方块位置
     */
    public static getBlockPositions(type: TetrisBlockType, rotation: RotationState): Vec2[] {
        return this.BLOCK_SHAPES[type][rotation].map(pos => new Vec2(pos.x, pos.y));
    }

    /**
     * 旋转方块
     */
    public static rotateBlock(blockData: TetrisBlockData): TetrisBlockData {
        const newRotation = (blockData.rotation + 1) % 4 as RotationState;
        return {
            ...blockData,
            rotation: newRotation,
            positions: this.getBlockPositions(blockData.type, newRotation)
        };
    }

    /**
     * 获取方块的边界框
     */
    public static getBoundingBox(positions: Vec2[]): { minX: number, maxX: number, minY: number, maxY: number } {
        let minX = Infinity, maxX = -Infinity;
        let minY = Infinity, maxY = -Infinity;

        for (const pos of positions) {
            minX = Math.min(minX, pos.x);
            maxX = Math.max(maxX, pos.x);
            minY = Math.min(minY, pos.y);
            maxY = Math.max(maxY, pos.y);
        }

        return { minX, maxX, minY, maxY };
    }

    /**
     * 检查方块是否可以放置在指定位置
     */
    public static canPlaceAt(positions: Vec2[], centerX: number, centerY: number, board: boolean[][]): boolean {
        const boardSize = board.length;
        
        for (const pos of positions) {
            const x = centerX + pos.x;
            const y = centerY + pos.y;
            
            // 检查是否超出边界
            if (x < 0 || x >= boardSize || y < 0 || y >= boardSize) {
                return false;
            }
            
            // 检查位置是否已被占用
            if (board[y][x]) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取方块数据
     */
    public get blockData(): TetrisBlockData {
        return this._blockData;
    }

    /**
     * 设置方块数据
     */
    public set blockData(data: TetrisBlockData) {
        this._blockData = data;
    }
}
