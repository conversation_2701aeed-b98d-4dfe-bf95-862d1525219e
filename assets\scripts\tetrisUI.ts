import { _decorator, Component, Label, Node, Prefab } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('tetrisUI')
export class tetrisUI extends Component {
    @property(Node)
    public boardContainer: Node = null;

    @property(Node)
    public blockQueueContainer: Node = null;

    @property(Label)
    public scoreLabel: Label = null;

    @property(Label)
    public comboLabel: Label = null;

    @property(Node)
    public rowTargetsContainer: Node = null;

    @property(Node)
    public colTargetsContainer: Node = null;

    @property(Prefab)
    public cellPrefab: Prefab = null;

    @property(Prefab)
    public blockPrefab: Prefab = null;

    @property(Prefab)
    public targetLabelPrefab: Prefab = null;

    @property
    public cellSize: number = 50;

    @property
    public boardSpacing: number = 2;

    start() {

    }

    update(deltaTime: number) {
        
    }
}


