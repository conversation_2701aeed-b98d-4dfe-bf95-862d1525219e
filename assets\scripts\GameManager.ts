import { _decorator, Component, Node, EventTouch, Vec3, input, Input } from 'cc';
import { GameBoard } from './GameBoard';
import { BlockGenerator } from './BlockGenerator';
import { DragDropSystem, DragEventData } from './DragDropSystem';
import { ClearSystem, ClearResult } from './ClearSystem';
import { GameUI } from './GameUI';
import { TetrisBlockData } from './TetrisBlock';
const { ccclass, property } = _decorator;

/**
 * 游戏状态枚举
 */
export enum GameState {
    MENU = 'menu',
    PLAYING = 'playing',
    PAUSED = 'paused',
    GAME_OVER = 'game_over'
}

/**
 * 游戏管理器
 */
@ccclass('GameManager')
export class GameManager extends Component {
    
    @property(Node)
    public gameBoardNode: Node = null;

    @property(Node)
    public uiNode: Node = null;

    @property
    public boardSize: number = 6;

    @property
    public maxBlocksInQueue: number = 3;

    // 系统组件
    private _gameBoard: GameBoard = null;
    private _blockGenerator: BlockGenerator = null;
    private _dragDropSystem: DragDropSystem = null;
    private _clearSystem: ClearSystem = null;
    private _gameUI: GameUI = null;

    // 游戏状态
    private _currentState: GameState = GameState.MENU;
    private _selectedBlockIndex: number = -1;
    private _isDragging: boolean = false;

    protected onLoad() {
        this.initializeComponents();
        this.setupEventListeners();
    }

    protected start() {
        this.startNewGame();
    }

    /**
     * 初始化所有组件
     */
    private initializeComponents(): void {
        // 初始化游戏棋盘
        if (this.gameBoardNode) {
            this._gameBoard = this.gameBoardNode.getComponent(GameBoard);
            if (!this._gameBoard) {
                this._gameBoard = this.gameBoardNode.addComponent(GameBoard);
            }
            this._gameBoard.setBoardSize(this.boardSize);
        }

        // 初始化方块生成器
        this._blockGenerator = this.node.getComponent(BlockGenerator);
        if (!this._blockGenerator) {
            this._blockGenerator = this.node.addComponent(BlockGenerator);
        }
        this._blockGenerator.setMaxBlocksInQueue(this.maxBlocksInQueue);

        // 初始化拖拽系统
        this._dragDropSystem = this.node.getComponent(DragDropSystem);
        if (!this._dragDropSystem) {
            this._dragDropSystem = this.node.addComponent(DragDropSystem);
        }
        this._dragDropSystem.gameBoard = this.gameBoardNode;

        // 初始化消除系统
        if (this.gameBoardNode) {
            this._clearSystem = this.gameBoardNode.getComponent(ClearSystem);
            if (!this._clearSystem) {
                this._clearSystem = this.gameBoardNode.addComponent(ClearSystem);
            }
        }

        // 初始化UI
        if (this.uiNode) {
            this._gameUI = this.uiNode.getComponent(GameUI);
            if (!this._gameUI) {
                this._gameUI = this.uiNode.addComponent(GameUI);
            }
        }
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        // 设置拖拽事件回调
        if (this._dragDropSystem) {
            this._dragDropSystem.setDragStartCallback(this.onDragStart.bind(this));
            this._dragDropSystem.setDragMoveCallback(this.onDragMove.bind(this));
            this._dragDropSystem.setDragEndCallback(this.onDragEnd.bind(this));
        }

        // 设置消除事件回调
        if (this._clearSystem) {
            this._clearSystem.setClearStartCallback(this.onClearStart.bind(this));
            this._clearSystem.setClearCompleteCallback(this.onClearComplete.bind(this));
            this._clearSystem.setScoreUpdateCallback(this.onScoreUpdate.bind(this));
        }

        // 设置输入事件
        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
    }

    /**
     * 开始新游戏
     */
    public startNewGame(): void {
        this._currentState = GameState.PLAYING;
        
        // 重置所有系统
        if (this._gameBoard) {
            this._gameBoard.resetBoard();
        }
        
        if (this._blockGenerator) {
            this._blockGenerator.resetQueue();
        }
        
        if (this._clearSystem) {
            this._clearSystem.resetScore();
        }

        // 更新UI
        if (this._gameUI) {
            this._gameUI.updateUI();
        }

        this._selectedBlockIndex = -1;
        this._isDragging = false;
    }

    /**
     * 暂停游戏
     */
    public pauseGame(): void {
        if (this._currentState === GameState.PLAYING) {
            this._currentState = GameState.PAUSED;
        }
    }

    /**
     * 恢复游戏
     */
    public resumeGame(): void {
        if (this._currentState === GameState.PAUSED) {
            this._currentState = GameState.PLAYING;
        }
    }

    /**
     * 游戏结束
     */
    public gameOver(): void {
        this._currentState = GameState.GAME_OVER;
        console.log('Game Over!');
        // 这里可以显示游戏结束界面
    }

    /**
     * 触摸开始事件
     */
    private onTouchStart(event: EventTouch): void {
        if (this._currentState !== GameState.PLAYING || this._isDragging) {
            return;
        }

        const touchPos = event.getUILocation();
        const worldPos = new Vec3(touchPos.x, touchPos.y, 0);

        // 检查是否点击了方块队列中的方块
        const blockIndex = this.getBlockIndexAtPosition(worldPos);
        if (blockIndex >= 0) {
            this._selectedBlockIndex = blockIndex;
            const blockData = this._blockGenerator.getBlockAt(blockIndex);
            if (blockData) {
                // 开始拖拽
                const blockNode = this.getBlockNodeAt(blockIndex);
                if (blockNode) {
                    this._dragDropSystem.startDrag(blockData, blockNode, worldPos);
                    this._isDragging = true;
                }
            }
        }
    }

    /**
     * 触摸移动事件
     */
    private onTouchMove(event: EventTouch): void {
        if (this._currentState !== GameState.PLAYING || !this._isDragging) {
            return;
        }

        const touchPos = event.getUILocation();
        const worldPos = new Vec3(touchPos.x, touchPos.y, 0);
        this._dragDropSystem.updateDrag(worldPos);
    }

    /**
     * 触摸结束事件
     */
    private onTouchEnd(event: EventTouch): void {
        if (this._currentState !== GameState.PLAYING || !this._isDragging) {
            return;
        }

        const success = this._dragDropSystem.endDrag();
        this._isDragging = false;

        if (success && this._selectedBlockIndex >= 0) {
            // 移除已放置的方块
            this._blockGenerator.removeBlockAt(this._selectedBlockIndex);
            
            // 检查消除
            this.checkForClears();
            
            // 更新UI
            if (this._gameUI) {
                this._gameUI.updateUI();
            }

            // 检查游戏是否结束
            this.checkGameOver();
        }

        this._selectedBlockIndex = -1;
    }

    /**
     * 检查消除
     */
    private checkForClears(): void {
        if (this._clearSystem) {
            const result = this._clearSystem.checkAndClear();
            if (result.totalCleared > 0) {
                console.log(`Cleared ${result.totalCleared} lines, Score: ${result.score}`);
            }
        }
    }

    /**
     * 检查游戏是否结束
     */
    private checkGameOver(): void {
        if (this._gameBoard && this._gameBoard.isGameOver()) {
            this.gameOver();
        }
    }

    /**
     * 拖拽开始回调
     */
    private onDragStart(data: DragEventData): void {
        console.log('Drag started');
    }

    /**
     * 拖拽移动回调
     */
    private onDragMove(data: DragEventData): void {
        // 可以在这里添加视觉反馈
    }

    /**
     * 拖拽结束回调
     */
    private onDragEnd(data: DragEventData, success: boolean): void {
        console.log(`Drag ended, success: ${success}`);
    }

    /**
     * 消除开始回调
     */
    private onClearStart(result: ClearResult): void {
        console.log('Clear started', result);
    }

    /**
     * 消除完成回调
     */
    private onClearComplete(result: ClearResult): void {
        console.log('Clear completed', result);
        // 更新UI
        if (this._gameUI) {
            this._gameUI.updateUI();
        }
    }

    /**
     * 分数更新回调
     */
    private onScoreUpdate(score: number, combo: number): void {
        if (this._gameUI) {
            this._gameUI.updateScore(score, combo);
        }
    }

    /**
     * 获取指定位置的方块索引
     */
    private getBlockIndexAtPosition(worldPos: Vec3): number {
        // 这里需要根据实际的UI布局来实现
        // 简化实现，返回-1表示没有点击到方块
        return -1;
    }

    /**
     * 获取指定索引的方块节点
     */
    private getBlockNodeAt(index: number): Node | null {
        // 这里需要根据实际的UI实现来获取方块节点
        return null;
    }

    /**
     * 获取当前游戏状态
     */
    public getCurrentState(): GameState {
        return this._currentState;
    }

    /**
     * 旋转当前选中的方块
     */
    public rotateSelectedBlock(): void {
        if (this._selectedBlockIndex >= 0 && this._blockGenerator) {
            this._blockGenerator.rotateBlockAt(this._selectedBlockIndex);
            if (this._gameUI) {
                this._gameUI.updateUI();
            }
        }
    }
}
