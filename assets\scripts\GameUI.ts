import { _decorator, Component, Node, Label, Sprite, Color, UITransform, Vec3, instantiate, Prefab, Vec2 } from 'cc';
import { GameBoard, CellState, TargetData } from './GameBoard';
import { TetrisBlockData } from './TetrisBlock';
import { BlockGenerator } from './BlockGenerator';
const { ccclass, property } = _decorator;

/**
 * 游戏UI管理器
 */
@ccclass('GameUI')
export class GameUI extends Component {
    
    @property(Node)
    public boardContainer: Node = null;

    @property(Node)
    public blockQueueContainer: Node = null;

    @property(Label)
    public scoreLabel: Label = null;

    @property(Label)
    public comboLabel: Label = null;

    @property(Node)
    public rowTargetsContainer: Node = null;

    @property(Node)
    public colTargetsContainer: Node = null;

    @property(Prefab)
    public cellPrefab: Prefab = null;

    @property(Prefab)
    public blockPrefab: Prefab = null;

    @property(Prefab)
    public targetLabelPrefab: Prefab = null;

    @property
    public cellSize: number = 50;

    @property
    public boardSpacing: number = 2;

    private _gameBoard: GameBoard = null;
    private _blockGenerator: BlockGenerator = null;
    private _cellNodes: Node[][] = [];
    private _blockQueueNodes: Node[] = [];
    private _rowTargetLabels: Label[] = [];
    private _colTargetLabels: Label[] = [];

    protected onLoad() {
        this.initializeComponents();
    }

    protected start() {
        this.createBoardUI();
        this.updateUI();
    }

    /**
     * 初始化组件引用
     */
    private initializeComponents(): void {
        // 获取游戏棋盘组件
        this._gameBoard = this.node.getComponent(GameBoard) || 
                         this.node.getComponentInChildren(GameBoard);
        
        // 获取方块生成器组件
        this._blockGenerator = this.node.getComponent(BlockGenerator) || 
                              this.node.getComponentInChildren(BlockGenerator);
    }

    /**
     * 创建棋盘UI
     */
    private createBoardUI(): void {
        if (!this._gameBoard || !this.boardContainer) {
            return;
        }

        const boardSize = this._gameBoard.getBoardSize();
        this._cellNodes = [];

        // 清空现有的UI
        this.boardContainer.removeAllChildren();

        // 创建棋盘格子
        for (let row = 0; row < boardSize; row++) {
            this._cellNodes[row] = [];
            for (let col = 0; col < boardSize; col++) {
                const cellNode = this.createCellNode(row, col);
                this.boardContainer.addChild(cellNode);
                this._cellNodes[row][col] = cellNode;
            }
        }

        // 创建目标数显示
        this.createTargetLabels();
    }

    /**
     * 创建单个格子节点
     */
    private createCellNode(row: number, col: number): Node {
        let cellNode: Node;
        
        if (this.cellPrefab) {
            cellNode = instantiate(this.cellPrefab);
        } else {
            // 如果没有预制体，创建简单的节点
            cellNode = new Node(`Cell_${row}_${col}`);
            const sprite = cellNode.addComponent(Sprite);
            const transform = cellNode.addComponent(UITransform);
            transform.setContentSize(this.cellSize, this.cellSize);
        }

        // 设置位置
        const boardSize = this._gameBoard.getBoardSize();
        const totalWidth = boardSize * this.cellSize + (boardSize - 1) * this.boardSpacing;
        const totalHeight = boardSize * this.cellSize + (boardSize - 1) * this.boardSpacing;
        
        const startX = -totalWidth / 2 + this.cellSize / 2;
        const startY = totalHeight / 2 - this.cellSize / 2;
        
        const x = startX + col * (this.cellSize + this.boardSpacing);
        const y = startY - row * (this.cellSize + this.boardSpacing);
        
        cellNode.setPosition(new Vec3(x, y, 0));

        return cellNode;
    }

    /**
     * 创建目标数标签
     */
    private createTargetLabels(): void {
        if (!this._gameBoard) {
            return;
        }

        const boardSize = this._gameBoard.getBoardSize();
        const targets = this._gameBoard.getTargets();

        // 清空现有标签
        this._rowTargetLabels = [];
        this._colTargetLabels = [];

        if (this.rowTargetsContainer) {
            this.rowTargetsContainer.removeAllChildren();
        }
        if (this.colTargetsContainer) {
            this.colTargetsContainer.removeAllChildren();
        }

        // 创建行目标标签
        for (let row = 0; row < boardSize; row++) {
            const label = this.createTargetLabel(targets.rowTargets[row].toString());
            if (this.rowTargetsContainer) {
                this.rowTargetsContainer.addChild(label);
            }
            this._rowTargetLabels.push(label.getComponent(Label));
        }

        // 创建列目标标签
        for (let col = 0; col < boardSize; col++) {
            const label = this.createTargetLabel(targets.colTargets[col].toString());
            if (this.colTargetsContainer) {
                this.colTargetsContainer.addChild(label);
            }
            this._colTargetLabels.push(label.getComponent(Label));
        }
    }

    /**
     * 创建目标标签节点
     */
    private createTargetLabel(text: string): Node {
        let labelNode: Node;
        
        if (this.targetLabelPrefab) {
            labelNode = instantiate(this.targetLabelPrefab);
        } else {
            labelNode = new Node('TargetLabel');
            labelNode.addComponent(UITransform);
            labelNode.addComponent(Label);
        }

        const label = labelNode.getComponent(Label);
        if (label) {
            label.string = text;
        }

        return labelNode;
    }

    /**
     * 更新UI显示
     */
    public updateUI(): void {
        this.updateBoardDisplay();
        this.updateBlockQueue();
        this.updateTargetLabels();
    }

    /**
     * 更新棋盘显示
     */
    private updateBoardDisplay(): void {
        if (!this._gameBoard || !this._cellNodes.length) {
            return;
        }

        const boardState = this._gameBoard.getBoardState();
        const boardSize = this._gameBoard.getBoardSize();

        for (let row = 0; row < boardSize; row++) {
            for (let col = 0; col < boardSize; col++) {
                const cellNode = this._cellNodes[row][col];
                const cellState = boardState[row][col];
                
                if (cellNode) {
                    this.updateCellDisplay(cellNode, cellState);
                }
            }
        }
    }

    /**
     * 更新单个格子显示
     */
    private updateCellDisplay(cellNode: Node, cellState: CellState): void {
        const sprite = cellNode.getComponent(Sprite);
        if (sprite) {
            if (cellState.filled) {
                sprite.color = Color.fromHEX(new Color(), cellState.color);
            } else {
                sprite.color = Color.WHITE;
            }
        }
    }

    /**
     * 更新方块队列显示
     */
    private updateBlockQueue(): void {
        if (!this._blockGenerator || !this.blockQueueContainer) {
            return;
        }

        // 清空现有的方块显示
        this._blockQueueNodes.forEach(node => node.destroy());
        this._blockQueueNodes = [];

        const blockQueue = this._blockGenerator.getBlockQueue();
        
        blockQueue.forEach((blockData, index) => {
            const blockNode = this.createBlockNode(blockData, index);
            this.blockQueueContainer.addChild(blockNode);
            this._blockQueueNodes.push(blockNode);
        });
    }

    /**
     * 创建方块节点
     */
    private createBlockNode(blockData: TetrisBlockData, index: number): Node {
        let blockNode: Node;
        
        if (this.blockPrefab) {
            blockNode = instantiate(this.blockPrefab);
        } else {
            blockNode = new Node(`Block_${index}`);
            blockNode.addComponent(UITransform);
        }

        // 设置位置
        const spacing = 100;
        blockNode.setPosition(new Vec3(index * spacing, 0, 0));

        // 创建方块的格子
        blockData.positions.forEach(pos => {
            const cellNode = this.createBlockCell(pos, blockData.color);
            blockNode.addChild(cellNode);
        });

        return blockNode;
    }

    /**
     * 创建方块格子
     */
    private createBlockCell(position: Vec2, color: string): Node {
        const cellNode = new Node('BlockCell');
        const sprite = cellNode.addComponent(Sprite);
        const transform = cellNode.addComponent(UITransform);
        
        transform.setContentSize(this.cellSize * 0.8, this.cellSize * 0.8);
        sprite.color = Color.fromHEX(new Color(), color);
        
        cellNode.setPosition(new Vec3(
            position.x * this.cellSize * 0.8,
            position.y * this.cellSize * 0.8,
            0
        ));

        return cellNode;
    }

    /**
     * 更新目标标签显示
     */
    private updateTargetLabels(): void {
        if (!this._gameBoard) {
            return;
        }

        const boardSize = this._gameBoard.getBoardSize();

        // 更新行目标
        for (let row = 0; row < boardSize && row < this._rowTargetLabels.length; row++) {
            const filledCount = this._gameBoard.getRowFilledCount(row);
            const targetCount = this._gameBoard.getTargets().rowTargets[row];
            const label = this._rowTargetLabels[row];
            
            if (label) {
                label.string = `${filledCount}/${targetCount}`;
                label.color = filledCount === targetCount ? Color.GREEN : Color.WHITE;
            }
        }

        // 更新列目标
        for (let col = 0; col < boardSize && col < this._colTargetLabels.length; col++) {
            const filledCount = this._gameBoard.getColFilledCount(col);
            const targetCount = this._gameBoard.getTargets().colTargets[col];
            const label = this._colTargetLabels[col];
            
            if (label) {
                label.string = `${filledCount}/${targetCount}`;
                label.color = filledCount === targetCount ? Color.GREEN : Color.WHITE;
            }
        }
    }

    /**
     * 更新分数显示
     */
    public updateScore(score: number, combo: number): void {
        if (this.scoreLabel) {
            this.scoreLabel.string = `Score: ${score}`;
        }
        
        if (this.comboLabel) {
            this.comboLabel.string = combo > 1 ? `Combo: ${combo}` : '';
        }
    }

    /**
     * 获取棋盘容器
     */
    public getBoardContainer(): Node {
        return this.boardContainer;
    }

    /**
     * 获取方块队列容器
     */
    public getBlockQueueContainer(): Node {
        return this.blockQueueContainer;
    }
}
